package com.snct.dctcore.commoncore.analysis;

import com.alibaba.fastjson2.JSONObject;
import com.snct.dctcore.commoncore.domain.KafkaMessage;
import com.snct.dctcore.commoncore.domain.hbase.AttitudeHbaseVo;
import com.snct.dctcore.commoncore.domain.hbase.AttitudeHbaseVo2;
import com.snct.dctcore.commoncore.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.Optional;

/**
 * @ClassName: AttitudeAnalysis
 * @Description: 姿态数据解析
 * @author: wzewei
 * @date: 2025-09-05 09:28:24
 */
public class AttitudeAnalysis {
    protected static Logger logger = LoggerFactory.getLogger(AttitudeAnalysis.class);

    /**
     * 解析姿态数据
     *
     * @param kafkaMessage
     * @param type
     * @return
     */
    public static Object getAttitudeData(KafkaMessage kafkaMessage, int type) {
        if (type == 1) { // KST
            return getAttitudeData(kafkaMessage);
        }
        if (type == 2) { // JMS
            return getAttitudeData2(kafkaMessage);
        }
        return null;
    }

    /**
     * 解析JSON格式的姿态数据(KST)
     *
     * @param kafkaMessage
     * @return
     */
    public static AttitudeHbaseVo getAttitudeData(KafkaMessage kafkaMessage) {
        if (kafkaMessage == null || StringUtils.isBlank(kafkaMessage.getMsg())) {
            return null;
        }
        try {
            String msg = kafkaMessage.getMsg();
            JSONObject jsonObject = JSONObject.parseObject(msg);
            if (jsonObject == null || jsonObject.isEmpty()) {
                return null;
            }
            AttitudeHbaseVo attitudeHbaseVo = new AttitudeHbaseVo();
            long currentTime = Optional.ofNullable(kafkaMessage.getInitialTime())
                    .orElseGet(System::currentTimeMillis);
            Optional<Long> wholeSecondOpt = Optional.ofNullable(DateUtils.fetchWholeSecond(currentTime));
            attitudeHbaseVo.setInitialTime(wholeSecondOpt.map(Object::toString).orElse(null));
            attitudeHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(currentTime, "yyyy-MM-dd HH:mm:ss"));
            attitudeHbaseVo.setRolling(jsonObject.getString("rolling"));
            attitudeHbaseVo.setPitch(jsonObject.getString("pitch"));
            attitudeHbaseVo.setHeading(jsonObject.getString("heading"));
            attitudeHbaseVo.setLat(jsonObject.getString("lat"));
            attitudeHbaseVo.setHeight(jsonObject.getString("height"));
            attitudeHbaseVo.setDistance(jsonObject.getString("distance"));

            String lonValue = jsonObject.getString("lon");
            if (StringUtils.isBlank(lonValue)) {
                lonValue = jsonObject.getString("lng");
            }
            attitudeHbaseVo.setLon(lonValue);

            return attitudeHbaseVo;
        } catch (Exception e) {
            logger.error("姿态数据解析出错, 消息内容: [{}], 异常详情:", kafkaMessage.getMsg(), e);
        }
        return null;
    }

    /**
     * 解析GPYBM格式的姿态数据(JMS)
     *
     * @param kafkaMessage
     * @return
     */
    public static AttitudeHbaseVo2 getAttitudeData2(KafkaMessage kafkaMessage) {

        if (kafkaMessage == null || StringUtils.isBlank(kafkaMessage.getMsg())) {
            return null;
        }

        AttitudeHbaseVo2 attitudeHbaseVo2 = null;

        try {
            // 检查是否为GPYBM格式数据
            if (!kafkaMessage.getMsg().startsWith("$GPYBM")) {
                return null;
            }

            //当前时间
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() :
                    kafkaMessage.getInitialTime();

            // 分割数据，去掉校验和部分
            String dataWithoutChecksum = kafkaMessage.getMsg();
            if (dataWithoutChecksum.contains("*")) {
                dataWithoutChecksum = dataWithoutChecksum.substring(0, dataWithoutChecksum.lastIndexOf("*"));
            }

            String[] fields = dataWithoutChecksum.split(",", -1);

            if (fields.length < 24) {
                return null;
            }

            attitudeHbaseVo2 = new AttitudeHbaseVo2();
            attitudeHbaseVo2.setInitialTime(Objects.requireNonNull(DateUtils.fetchWholeSecond(currentTime)).toString());
            attitudeHbaseVo2.setInitialBjTime(DateUtils.parseTimeToDate(currentTime, "yyyy-MM-dd HH:mm:ss"));

            // 按照对照表解析各个字段
            attitudeHbaseVo2.setSerialNo(fields[1]);           // 设备序列号
            attitudeHbaseVo2.setUtc(fields[2]);               // UTC时间
            attitudeHbaseVo2.setLat(fields[3].replace("+", ""));               // 纬度
            attitudeHbaseVo2.setLon(fields[4].replace("+", ""));               // 经度
            attitudeHbaseVo2.setElpHeight(fields[5]);         // 椭球高
            attitudeHbaseVo2.setHeading(fields[6]);           // 航向角
            attitudeHbaseVo2.setPitch(fields[7]);             // 俯仰角
            attitudeHbaseVo2.setVelN(fields[8]);              // 北方向速度
            attitudeHbaseVo2.setVelE(fields[9]);              // 东方向速度
            attitudeHbaseVo2.setVelD(fields[10]);             // 地向速度
            attitudeHbaseVo2.setVelG(fields[11]);             // 地面速度
            attitudeHbaseVo2.setCoordinateNorthing(fields[12]); // 高精度坐标北向
            attitudeHbaseVo2.setCoordinateEasting(fields[13]);  // 高精度坐标东向
            attitudeHbaseVo2.setNorthDistance(fields[14]);    // 北距离
            attitudeHbaseVo2.setEastDistance(fields[15]);     // 东距离
            attitudeHbaseVo2.setPositionIndicator(fields[16]); // 定位指示状态
            attitudeHbaseVo2.setHeadingIndicator(fields[17]);  // 定向指示状态
            attitudeHbaseVo2.setSvn(fields[18]);              // 主导天线收星数
            attitudeHbaseVo2.setDiffAge(fields[19]);          // 差分延迟
            attitudeHbaseVo2.setStationId(fields[20]);        // 基准站ID
            attitudeHbaseVo2.setBaselineLength(fields[21]);   // 基线长度
            attitudeHbaseVo2.setSolutionSv(fields[22]);       // 从站参与解算的卫星数
            attitudeHbaseVo2.setRolling(fields[23]);          // 横滚角

        } catch (Exception e) {
            logger.error("姿态数据解析出错, 消息内容: [{}], 异常详情:", kafkaMessage.getMsg(), e);
        }
        return attitudeHbaseVo2;
    }
}
