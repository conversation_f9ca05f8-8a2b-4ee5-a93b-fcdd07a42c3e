package com.snct.dctcore.commoncore.analysis;


import com.snct.dctcore.commoncore.domain.KafkaMessage;
import com.snct.dctcore.commoncore.domain.hbase.AwsHbaseVo;
import com.snct.dctcore.commoncore.utils.AnalysisUtils;
import com.snct.dctcore.commoncore.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;


/**
 * @ClassName: AwsAnalysis
 * @Description: AWS数据解析
 * @author: wzewei
 * @date: 2025-09-05 09:28:24
 */
public class AwsAnalysis {

    protected static Logger logger = LoggerFactory.getLogger(AwsAnalysis.class);

    /**
     * 解析AWS数据
     * $WIMWV,257,R,2.0,M,A*3C
     * $WIXDR,C,17.0,C,0,H,74.0,P,0,P,1014.3,H,0*7C
     *
     * @param kafkaMessage
     * @param object
     * @return
     */
    public static AwsHbaseVo getAwsData(KafkaMessage kafkaMessage, Object object) {
        AwsHbaseVo awsHbaseVo;
        if (object == null) {
            awsHbaseVo = new AwsHbaseVo();
        } else {
            awsHbaseVo = (AwsHbaseVo) object;
        }
        try {
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() :
                    kafkaMessage.getInitialTime();
            awsHbaseVo.setInitialTime(Objects.requireNonNull(DateUtils.fetchWholeSecond(currentTime)).toString());
            awsHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));

            String[] values = AnalysisUtils.valuesTrim(kafkaMessage.getMsg().split(",", -1));
            switch (values[0]) {
                case "$WIMWV": {
                    try {
                        if ("R".equals(values[2])) {
                            awsHbaseVo.setRelativeWind(values[1]);
                            awsHbaseVo.setWindLogoR(values[2]);
                            awsHbaseVo.setRelativeWindSpeed(values[3]);
                        } else if ("T".equals(values[2])) {
                            awsHbaseVo.setTrueWind(values[1]);
                            awsHbaseVo.setWindLogoT(values[2]);
                            awsHbaseVo.setTrueWindSpeed(values[3]);
                        }
                        awsHbaseVo.setWindSpeedUnit(values[4]);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
                }
                case "$WIXDR": {
                    awsHbaseVo.setAirTemType(values[1]);
                    awsHbaseVo.setAirTemperature(values[2]);
                    awsHbaseVo.setAirUnit(values[3]);
                    awsHbaseVo.setAirSensor(values[4]);
                    awsHbaseVo.setHumidityType(values[5]);
                    awsHbaseVo.setHumidity(values[6]);
                    awsHbaseVo.setHumidityUnit(values[7]);
                    awsHbaseVo.setHumiditySensor(values[8]);
                    awsHbaseVo.setPressureType(values[9]);
                    awsHbaseVo.setPressure(values[10]);
                    awsHbaseVo.setPressureSensor(values[11]);
                    break;
                }
            }
        } catch (Exception e) {
            logger.error("AWS数据解析出错, 消息内容: [{}], 异常详情:", kafkaMessage.getMsg(), e);
        }
        return awsHbaseVo;
    }
}
